using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EAMS.Infrastructure.Repositories
{
    public class AmenityRepository : Repository<Amenity>, IAmenityRepository
    {
        public AmenityRepository(EamsDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Amenity>> GetAmenitiesWithOptionsAsync(CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(a => a.AmenityOptions)
                .ToListAsync(cancellationToken);
        }

        public async Task<Amenity?> GetAmenityWithOptionsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(a => a.AmenityOptions)
                .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
        }

        public async Task<IEnumerable<Amenity>> GetAmenitiesByTypeAsync(AmenityType amenityType, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(a => a.AmenityType == amenityType)
                .Include(a => a.AmenityOptions)
                .ToListAsync(cancellationToken);
        }
    }
}
