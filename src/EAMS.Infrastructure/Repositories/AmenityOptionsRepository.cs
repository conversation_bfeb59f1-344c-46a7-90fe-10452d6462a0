using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EAMS.Infrastructure.Repositories
{
    public class AmenityOptionsRepository : Repository<AmenityOptions>, IAmenityOptionsRepository
    {
        public AmenityOptionsRepository(EamsDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<AmenityOptions>> GetOptionsByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(ao => ao.AmenityId == amenityId)
                .ToListAsync(cancellationToken);
        }

        public async Task<AmenityOptions?> GetOptionWithAmenityAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(ao => ao.Amenity)
                .FirstOrDefaultAsync(ao => ao.Id == id, cancellationToken);
        }
    }
}
