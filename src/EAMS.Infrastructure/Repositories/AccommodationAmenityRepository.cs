using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EAMS.Infrastructure.Repositories
{
    public class AccommodationAmenityRepository : Repository<AccommodationAmenity>, IAccommodationAmenityRepository
    {
        public AccommodationAmenityRepository(EamsDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetByAccommodationIdAsync(Int64 accommodationId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(aa => aa.AccommodationId == accommodationId)
                .Include(aa => aa.Amenity)
                .Include(aa => aa.AmenityOption)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(aa => aa.AmenityId == amenityId)
                .Include(aa => aa.Accommodation)
                .Include(aa => aa.AmenityOption)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetByAmenityOptionIdAsync(Int64 amenityOptionId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(aa => aa.AmenityOptionId == amenityOptionId)
                .Include(aa => aa.Accommodation)
                .Include(aa => aa.Amenity)
                .ToListAsync(cancellationToken);
        }

        public async Task<AccommodationAmenity?> GetWithDetailsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(aa => aa.Accommodation)
                .Include(aa => aa.Amenity)
                .Include(aa => aa.AmenityOption)
                .FirstOrDefaultAsync(aa => aa.Id == id, cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetAllWithDetailsAsync(CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(aa => aa.Accommodation)
                .Include(aa => aa.Amenity)
                .Include(aa => aa.AmenityOption)
                .ToListAsync(cancellationToken);
        }

        public async Task<bool> ExistsAsync(Int64 accommodationId, Int64 amenityId, Int64 amenityOptionId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .AnyAsync(aa => aa.AccommodationId == accommodationId && 
                               aa.AmenityId == amenityId && 
                               aa.AmenityOptionId == amenityOptionId, cancellationToken);
        }
    }
}
