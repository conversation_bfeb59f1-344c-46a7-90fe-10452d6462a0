using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace EAMS.Infrastructure.Repositories
{
    public class AccommodationRepository : Repository<Accommodation>, IAccommodationRepository
    {
        public AccommodationRepository(EamsDbContext context) : base(context)
        {
        }

        public override async Task<IEnumerable<Accommodation>> GetAllAsync()
        {
            return await _dbSet
                .Where(a => !a.Inactive)
                .ToListAsync();
        }

        public override async Task<Accommodation?> GetByIdAsync(Int64 id)
        {
            return await _dbSet
                .FirstOrDefaultAsync(a => a.Id == id && !a.Inactive);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsNearLocationAsync(
            double latitude, double longitude, double radiusKm, CancellationToken cancellationToken = default)
        {
            // For now, use in-memory filtering with the GeoPoint distance calculation
            // In production, you might want to use raw SQL with spatial functions for better performance
            var allAccommodations = await _dbSet
                .Where(a => !a.Inactive && a.Location != null)
                .ToListAsync(cancellationToken);

            var searchPoint = GeoPoint.Create(latitude, longitude);

            return allAccommodations
                .Where(a => a.Location != null && a.Location.IsWithinRadius(searchPoint, radiusKm))
                .OrderBy(a => a.Location!.DistanceTo(searchPoint))
                .ToList();
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsByRegionAsync(
            Region region, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(a => !a.Inactive && a.Region == region)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsByTypeAsync(
            AccommodationType accommodationType, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(a => !a.Inactive && a.AccommodationType == accommodationType)
                .ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsWithLocationAsync(
            CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(a => !a.Inactive && a.Location != null)
                .ToListAsync(cancellationToken);
        }
    }
}
