using EAMS.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EAMS.Infrastructure.Configurations
{
    public class AmenityOptionsConfiguration : IEntityTypeConfiguration<AmenityOptions>
    {
        public void Configure(EntityTypeBuilder<AmenityOptions> builder)
        {
            builder.HasKey(ao => ao.Id);

            builder.Property(ao => ao.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(ao => ao.DisplayText)
                .IsRequired()
                .HasMaxLength(300);

            builder.Property(ao => ao.Icon)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ao => ao.Color)
                .IsRequired()
                .HasMaxLength(50);

            // Configure relationships
            builder.HasOne(ao => ao.Amenity)
                .WithMany(a => a.AmenityOptions)
                .HasForeignKey(ao => ao.AmenityId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(ao => ao.AccommodationAmenities)
                .WithOne(aa => aa.AmenityOption)
                .HasForeignKey(aa => aa.AmenityOptionId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
