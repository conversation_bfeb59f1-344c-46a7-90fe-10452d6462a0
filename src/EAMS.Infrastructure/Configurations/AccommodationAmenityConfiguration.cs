using EAMS.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EAMS.Infrastructure.Configurations
{
    public class AccommodationAmenityConfiguration : IEntityTypeConfiguration<AccommodationAmenity>
    {
        public void Configure(EntityTypeBuilder<AccommodationAmenity> builder)
        {
            builder.HasKey(aa => aa.Id);

            builder.Property(aa => aa.Note)
                .HasMaxLength(500);

            // Configure relationships
            builder.HasOne(aa => aa.Accommodation)
                .WithMany(a => a.AccommodationAmenities)
                .HasForeignKey(aa => aa.AccommodationId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(aa => aa.Amenity)
                .WithMany(a => a.AccommodationAmenities)
                .HasForeignKey(aa => aa.AmenityId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(aa => aa.AmenityOption)
                .WithMany(ao => ao.AccommodationAmenities)
                .HasForeignKey(aa => aa.AmenityOptionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Create unique index to prevent duplicate combinations
            builder.HasIndex(aa => new { aa.AccommodationId, aa.AmenityId, aa.AmenityOptionId })
                .IsUnique()
                .HasDatabaseName("IX_AccommodationAmenity_Unique");
        }
    }
}
