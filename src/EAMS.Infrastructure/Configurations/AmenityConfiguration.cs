using EAMS.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EAMS.Infrastructure.Configurations
{
    public class AmenityConfiguration : IEntityTypeConfiguration<Amenity>
    {
        public void Configure(EntityTypeBuilder<Amenity> builder)
        {
            builder.HasKey(a => a.Id);

            builder.Property(a => a.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(a => a.HelpText)
                .IsRequired()
                .HasMaxLength(1000);

            // Configure relationships
            builder.HasMany(a => a.AmenityOptions)
                .WithOne(ao => ao.Amenity)
                .HasForeignKey(ao => ao.AmenityId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(a => a.AccommodationAmenities)
                .WithOne(aa => aa.Amenity)
                .HasForeignKey(aa => aa.AmenityId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
