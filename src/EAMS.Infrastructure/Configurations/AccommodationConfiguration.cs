using EAMS.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EAMS.Infrastructure.Configurations
{
    public class AccommodationConfiguration : IEntityTypeConfiguration<Accommodation>
    {
        public void Configure(EntityTypeBuilder<Accommodation> builder)
        {
            builder.HasKey(a => a.Id);

            builder.Property(a => a.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(a => a.Street)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(a => a.Suburb)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(a => a.Postcode)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(a => a.State)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(a => a.Phone)
                .HasMaxLength(20);

            builder.Property(a => a.Email)
                .HasMaxLength(100);

            builder.Property(a => a.Website)
                .HasMaxLength(200);

            // Configure relationships
            builder.HasMany(a => a.AccommodationAmenities)
                .WithOne(aa => aa.Accommodation)
                .HasForeignKey(aa => aa.AccommodationId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
