using EAMS.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetTopologySuite.Geometries;

namespace EAMS.Infrastructure.Configurations
{
    public class AccommodationConfiguration : IEntityTypeConfiguration<Accommodation>
    {
        public void Configure(EntityTypeBuilder<Accommodation> builder)
        {
            builder.HasKey(a => a.Id);

            // Basic properties
            builder.Property(a => a.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(a => a.StreetLine1)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(a => a.StreetLine2)
                .HasMaxLength(200);

            builder.Property(a => a.Suburb)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(a => a.State)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(a => a.Postcode)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(a => a.Phone)
                .HasMaxLength(20);

            builder.Property(a => a.Email)
                .HasMaxLength(100);

            builder.Property(a => a.Website)
                .HasMaxLength(200);

            // Location configuration with spatial support
            builder.Property(a => a.Location)
                .HasConversion(
                    geoPoint => geoPoint == null ? null : 
                        new Point(geoPoint.Longitude, geoPoint.Latitude) { SRID = 4326 },
                    point => point == null ? null : 
                        GeoPoint.Create(point.Y, point.X))
                .HasColumnType("geography");

            // Create spatial index for efficient location queries
            builder.HasIndex(a => a.Location)
                .HasDatabaseName("IX_Accommodations_Location_Spatial");

            // Enum configurations
            builder.Property(a => a.Region)
                .HasConversion<int>();

            builder.Property(a => a.AccommodationType)
                .HasConversion<int>();

            builder.Property(a => a.Density)
                .HasConversion<int>();

            // Duration as JSON or separate table - for now as JSON
            builder.Property(a => a.Duration)
                .HasConversion(
                    v => string.Join(',', v.Select(d => (int)d)),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                          .Select(s => (Duration)int.Parse(s))
                          .ToList())
                .HasMaxLength(100);

            // Soft delete support
            builder.Property(a => a.Inactive)
                .HasDefaultValue(false);

            // Add query filter for soft delete
            builder.HasQueryFilter(a => !a.Inactive);
        }
    }
}
