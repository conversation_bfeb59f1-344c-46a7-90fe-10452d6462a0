using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace EAMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AccommodationAmenityController : ControllerBase
    {
        private readonly IAccommodationAmenityService _accommodationAmenityService;
        private readonly IMapper _mapper;
        private readonly ILogger<AccommodationAmenityController> _logger;

        public AccommodationAmenityController(
            IAccommodationAmenityService accommodationAmenityService,
            IMapper mapper,
            ILogger<AccommodationAmenityController> logger)
        {
            _accommodationAmenityService = accommodationAmenityService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Get all accommodation amenities
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AccommodationAmenityDto>>> GetAccommodationAmenities(
            [FromQuery] bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var accommodationAmenities = includeDetails
                    ? await _accommodationAmenityService.GetAllWithDetailsAsync(cancellationToken)
                    : await _accommodationAmenityService.GetAllAccommodationAmenitiesAsync(cancellationToken);
                
                var response = _mapper.Map<IEnumerable<AccommodationAmenityDto>>(accommodationAmenities);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodation amenities");
                return StatusCode(500, "An error occurred while retrieving accommodation amenities");
            }
        }

        /// <summary>
        /// Get accommodation amenities by accommodation ID
        /// </summary>
        [HttpGet("by-accommodation/{accommodationId}")]
        public async Task<ActionResult<IEnumerable<AccommodationAmenityDto>>> GetByAccommodationId(
            Int64 accommodationId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var accommodationAmenities = await _accommodationAmenityService.GetByAccommodationIdAsync(accommodationId, cancellationToken);
                var response = _mapper.Map<IEnumerable<AccommodationAmenityDto>>(accommodationAmenities);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodation amenities for accommodation {AccommodationId}", accommodationId);
                return StatusCode(500, "An error occurred while retrieving accommodation amenities");
            }
        }

        /// <summary>
        /// Get accommodation amenities by amenity ID
        /// </summary>
        [HttpGet("by-amenity/{amenityId}")]
        public async Task<ActionResult<IEnumerable<AccommodationAmenityDto>>> GetByAmenityId(
            Int64 amenityId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var accommodationAmenities = await _accommodationAmenityService.GetByAmenityIdAsync(amenityId, cancellationToken);
                var response = _mapper.Map<IEnumerable<AccommodationAmenityDto>>(accommodationAmenities);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodation amenities for amenity {AmenityId}", amenityId);
                return StatusCode(500, "An error occurred while retrieving accommodation amenities");
            }
        }

        /// <summary>
        /// Get accommodation amenities by amenity option ID
        /// </summary>
        [HttpGet("by-amenity-option/{amenityOptionId}")]
        public async Task<ActionResult<IEnumerable<AccommodationAmenityDto>>> GetByAmenityOptionId(
            Int64 amenityOptionId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var accommodationAmenities = await _accommodationAmenityService.GetByAmenityOptionIdAsync(amenityOptionId, cancellationToken);
                var response = _mapper.Map<IEnumerable<AccommodationAmenityDto>>(accommodationAmenities);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodation amenities for amenity option {AmenityOptionId}", amenityOptionId);
                return StatusCode(500, "An error occurred while retrieving accommodation amenities");
            }
        }

        /// <summary>
        /// Get accommodation amenity by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<AccommodationAmenityDto>> GetAccommodationAmenity(
            Int64 id,
            [FromQuery] bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var accommodationAmenity = includeDetails
                    ? await _accommodationAmenityService.GetWithDetailsAsync(id, cancellationToken)
                    : await _accommodationAmenityService.GetAccommodationAmenityByIdAsync(id, cancellationToken);

                if (accommodationAmenity == null)
                {
                    return NotFound($"Accommodation amenity with ID {id} not found");
                }

                var response = _mapper.Map<AccommodationAmenityDto>(accommodationAmenity);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodation amenity with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the accommodation amenity");
            }
        }

        /// <summary>
        /// Create a new accommodation amenity
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<AccommodationAmenityDto>> CreateAccommodationAmenity(
            AccommodationAmenityDto accommodationAmenityDto,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var accommodationAmenity = _mapper.Map<AccommodationAmenity>(accommodationAmenityDto);
                var createdAccommodationAmenity = await _accommodationAmenityService.CreateAccommodationAmenityAsync(accommodationAmenity, cancellationToken);
                var response = _mapper.Map<AccommodationAmenityDto>(createdAccommodationAmenity);

                return CreatedAtAction(nameof(GetAccommodationAmenity), new { id = createdAccommodationAmenity.Id }, response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation while creating accommodation amenity");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating accommodation amenity");
                return StatusCode(500, "An error occurred while creating the accommodation amenity");
            }
        }

        /// <summary>
        /// Update an existing accommodation amenity
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<AccommodationAmenityDto>> UpdateAccommodationAmenity(
            Int64 id,
            AccommodationAmenityDto accommodationAmenityDto,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var accommodationAmenity = _mapper.Map<AccommodationAmenity>(accommodationAmenityDto);
                var updatedAccommodationAmenity = await _accommodationAmenityService.UpdateAccommodationAmenityAsync(id, accommodationAmenity, cancellationToken);
                var response = _mapper.Map<AccommodationAmenityDto>(updatedAccommodationAmenity);

                return Ok(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Accommodation amenity with ID {Id} not found for update", id);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating accommodation amenity with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the accommodation amenity");
            }
        }

        /// <summary>
        /// Delete an accommodation amenity
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> DeleteAccommodationAmenity(Int64 id, CancellationToken cancellationToken = default)
        {
            try
            {
                var exists = await _accommodationAmenityService.AccommodationAmenityExistsAsync(id, cancellationToken);
                if (!exists)
                {
                    return NotFound(false);
                }

                var result = await _accommodationAmenityService.DeleteAccommodationAmenityAsync(id, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting accommodation amenity with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the accommodation amenity");
            }
        }

        /// <summary>
        /// Check if a combination exists
        /// </summary>
        [HttpGet("exists")]
        public async Task<ActionResult<bool>> CheckCombinationExists(
            [FromQuery] Int64 accommodationId,
            [FromQuery] Int64 amenityId,
            [FromQuery] Int64 amenityOptionId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var exists = await _accommodationAmenityService.CombinationExistsAsync(accommodationId, amenityId, amenityOptionId, cancellationToken);
                return Ok(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if combination exists");
                return StatusCode(500, "An error occurred while checking the combination");
            }
        }
    }
}
