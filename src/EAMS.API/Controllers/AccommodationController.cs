using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using AutoMapper;
using NetTopologySuite.Geometries;

namespace EAMS.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    public class AccommodationController : ControllerBase
    {
        private readonly IAccommodationService _accommodationService;
        private readonly ILogger<AccommodationController> _logger;
        private readonly IMapper _mapper;

        public AccommodationController(
            IAccommodationService accommodationService,
            ILogger<AccommodationController> logger,
            IMapper mapper)
        {
            _accommodationService = accommodationService;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all accommodations with optional filtering
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AccommodationDto>>> GetAccommodations(
            [FromQuery] Region? region = null,
            [FromQuery] AccommodationType? accommodationType = null,
            [FromQuery] bool onlyWithLocation = false,
            [FromQuery] double? latitude = null,
            [FromQuery] double? longitude = null,
            [FromQuery] double radiusKm = 10.0)
        {
            try
            {
                IEnumerable<Accommodation> accommodations;

                // Location-based search
                if (latitude.HasValue && longitude.HasValue)
                {
                    accommodations = await _accommodationService.GetAccommodationsNearLocationAsync(
                        latitude.Value, longitude.Value, radiusKm);
                }
                // Filter by region
                else if (region.HasValue)
                {
                    accommodations = await _accommodationService.GetAccommodationsByRegionAsync(region.Value);
                }
                // Filter by accommodation type
                else if (accommodationType.HasValue)
                {
                    accommodations = await _accommodationService.GetAccommodationsByTypeAsync(accommodationType.Value);
                }
                // Only accommodations with location
                else if (onlyWithLocation)
                {
                    accommodations = await _accommodationService.GetAccommodationsWithLocationAsync();
                }
                // Get all accommodations
                else
                {
                    accommodations = await _accommodationService.GetAllAccommodationsAsync();
                }

                var response = _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodations");
                return StatusCode(500, "An error occurred while retrieving accommodations");
            }
        }

        /// <summary>
        /// Get accommodation by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<AccommodationDto>> GetAccommodation(
            Int64 id)
        {
            try
            {
                var accommodation = await _accommodationService.GetAccommodationByIdAsync(id);

                if (accommodation == null)
                {
                    return NotFound($"Accommodation with ID {id} not found");
                }

                var response = _mapper.Map<AccommodationDto>(accommodation);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accommodation with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the accommodation");
            }
        }

        /// <summary>
        /// Create a new accommodation
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<AccommodationDto>> CreateAccommodation(
            AccommodationDto accommodationDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var accommodation = _mapper.Map<Accommodation>(accommodationDto);
                var createdAccommodation = await _accommodationService.CreateAccommodationAsync(accommodation);
                var response = _mapper.Map<AccommodationDto>(createdAccommodation);

                return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating accommodation");
                return StatusCode(500, "An error occurred while creating the accommodation");
            }
        }

        /// <summary>
        /// Update an existing accommodation
        /// </summary>
        [HttpPut]
        public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(AccommodationDto accommodationDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var accommodation = _mapper.Map<Accommodation>(accommodationDto);
                var updatedAccommodation = await _accommodationService.UpdateAccommodationAsync(accommodation);
                var response = _mapper.Map<AccommodationDto>(updatedAccommodation);

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Accommodation with ID {Id} not found for update", accommodationDto.Id);
                return NotFound($"Accommodation with ID {accommodationDto.Id} not found");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating accommodation with ID {Id}", accommodationDto.Id);
                return StatusCode(500, "An error occurred while updating the accommodation");
            }
        }

        /// <summary>
        /// Delete an accommodation
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> DeleteAccommodation(Int64 id)
        {
            try
            {
                var exists = await _accommodationService.AccommodationExistsAsync(id);
                if (!exists)
                {
                    return NotFound(false);
                }

                var result = await _accommodationService.DeleteAccommodationAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting accommodation with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the accommodation");
            }
        }

        /// <summary>
        /// Set location for an accommodation
        /// </summary>
        [HttpPut("{id}/location")]
        public async Task<ActionResult<AccommodationDto>> SetAccommodationLocation(
            Int64 id,
            [FromBody] LocationDto locationDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updatedAccommodation = await _accommodationService.SetAccommodationLocationAsync(
                    id, locationDto.Latitude, locationDto.Longitude);
                var response = _mapper.Map<AccommodationDto>(updatedAccommodation);

                return Ok(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Accommodation with ID {Id} not found for location update", id);
                return NotFound(ex.Message);
            }
            catch (ArgumentOutOfRangeException ex)
            {
                _logger.LogWarning(ex, "Invalid coordinates provided for accommodation {Id}", id);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting location for accommodation with ID {Id}", id);
                return StatusCode(500, "An error occurred while setting the accommodation location");
            }
        }

        /// <summary>
        /// Calculate distance between two accommodations
        /// </summary>
        [HttpGet("{id1}/distance-to/{id2}")]
        public async Task<ActionResult<DistanceResultDto>> CalculateDistance(Int64 id1, Int64 id2)
        {
            try
            {
                var distance = await _accommodationService.CalculateDistanceBetweenAccommodationsAsync(id1, id2);

                if (distance == null)
                {
                    return BadRequest("One or both accommodations do not have location information");
                }

                var result = new DistanceResultDto
                {
                    Accommodation1Id = id1,
                    Accommodation2Id = id2,
                    DistanceKm = distance.Value
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating distance between accommodations {Id1} and {Id2}", id1, id2);
                return StatusCode(500, "An error occurred while calculating the distance");
            }
        }
    }

    public class LocationDto
    {
        [Range(-90, 90, ErrorMessage = "Latitude must be between -90 and 90 degrees")]
        public double Latitude { get; set; }

        [Range(-180, 180, ErrorMessage = "Longitude must be between -180 and 180 degrees")]
        public double Longitude { get; set; }
    }

    public class DistanceResultDto
    {
        public Int64 Accommodation1Id { get; set; }
        public Int64 Accommodation2Id { get; set; }
        public double DistanceKm { get; set; }
    }
}
