using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace EAMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AmenityOptionsController : ControllerBase
    {
        private readonly IAmenityOptionsService _amenityOptionsService;
        private readonly IMapper _mapper;
        private readonly ILogger<AmenityOptionsController> _logger;

        public AmenityOptionsController(
            IAmenityOptionsService amenityOptionsService,
            IMapper mapper,
            ILogger<AmenityOptionsController> logger)
        {
            _amenityOptionsService = amenityOptionsService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Get all amenity options
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AmenityOptionsDto>>> GetAmenityOptions(CancellationToken cancellationToken = default)
        {
            try
            {
                var amenityOptions = await _amenityOptionsService.GetAllAmenityOptionsAsync(cancellationToken);
                var response = _mapper.Map<IEnumerable<AmenityOptionsDto>>(amenityOptions);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving amenity options");
                return StatusCode(500, "An error occurred while retrieving amenity options");
            }
        }

        /// <summary>
        /// Get amenity options by amenity ID
        /// </summary>
        [HttpGet("by-amenity/{amenityId}")]
        public async Task<ActionResult<IEnumerable<AmenityOptionsDto>>> GetOptionsByAmenityId(
            Int64 amenityId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var amenityOptions = await _amenityOptionsService.GetOptionsByAmenityIdAsync(amenityId, cancellationToken);
                var response = _mapper.Map<IEnumerable<AmenityOptionsDto>>(amenityOptions);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving amenity options for amenity {AmenityId}", amenityId);
                return StatusCode(500, "An error occurred while retrieving amenity options");
            }
        }

        /// <summary>
        /// Get amenity option by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<AmenityOptionsDto>> GetAmenityOption(
            Int64 id,
            [FromQuery] bool includeAmenity = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var amenityOption = includeAmenity
                    ? await _amenityOptionsService.GetAmenityOptionWithAmenityAsync(id, cancellationToken)
                    : await _amenityOptionsService.GetAmenityOptionByIdAsync(id, cancellationToken);

                if (amenityOption == null)
                {
                    return NotFound($"Amenity option with ID {id} not found");
                }

                var response = _mapper.Map<AmenityOptionsDto>(amenityOption);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving amenity option with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the amenity option");
            }
        }

        /// <summary>
        /// Create a new amenity option
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<AmenityOptionsDto>> CreateAmenityOption(
            AmenityOptionsDto amenityOptionDto,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var amenityOption = _mapper.Map<AmenityOptions>(amenityOptionDto);
                var createdAmenityOption = await _amenityOptionsService.CreateAmenityOptionAsync(amenityOption, cancellationToken);
                var response = _mapper.Map<AmenityOptionsDto>(createdAmenityOption);

                return CreatedAtAction(nameof(GetAmenityOption), new { id = createdAmenityOption.Id }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating amenity option");
                return StatusCode(500, "An error occurred while creating the amenity option");
            }
        }

        /// <summary>
        /// Update an existing amenity option
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<AmenityOptionsDto>> UpdateAmenityOption(
            Int64 id,
            AmenityOptionsDto amenityOptionDto,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var amenityOption = _mapper.Map<AmenityOptions>(amenityOptionDto);
                var updatedAmenityOption = await _amenityOptionsService.UpdateAmenityOptionAsync(id, amenityOption, cancellationToken);
                var response = _mapper.Map<AmenityOptionsDto>(updatedAmenityOption);

                return Ok(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Amenity option with ID {Id} not found for update", id);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating amenity option with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the amenity option");
            }
        }

        /// <summary>
        /// Delete an amenity option
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> DeleteAmenityOption(Int64 id, CancellationToken cancellationToken = default)
        {
            try
            {
                var exists = await _amenityOptionsService.AmenityOptionExistsAsync(id, cancellationToken);
                if (!exists)
                {
                    return NotFound(false);
                }

                var result = await _amenityOptionsService.DeleteAmenityOptionAsync(id, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting amenity option with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the amenity option");
            }
        }
    }
}
