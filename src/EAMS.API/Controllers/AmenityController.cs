using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace EAMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AmenityController : ControllerBase
    {
        private readonly IAmenityService _amenityService;
        private readonly IMapper _mapper;
        private readonly ILogger<AmenityController> _logger;

        public AmenityController(
            IAmenityService amenityService,
            IMapper mapper,
            ILogger<AmenityController> logger)
        {
            _amenityService = amenityService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Get all amenities
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AmenityDto>>> GetAmenities(
            [FromQuery] bool includeOptions = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var amenities = includeOptions 
                    ? await _amenityService.GetAmenitiesWithOptionsAsync(cancellationToken)
                    : await _amenityService.GetAllAmenitiesAsync(cancellationToken);
                
                var response = _mapper.Map<IEnumerable<AmenityDto>>(amenities);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving amenities");
                return StatusCode(500, "An error occurred while retrieving amenities");
            }
        }

        /// <summary>
        /// Get amenities by type
        /// </summary>
        [HttpGet("by-type/{amenityType}")]
        public async Task<ActionResult<IEnumerable<AmenityDto>>> GetAmenitiesByType(
            AmenityType amenityType,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var amenities = await _amenityService.GetAmenitiesByTypeAsync(amenityType, cancellationToken);
                var response = _mapper.Map<IEnumerable<AmenityDto>>(amenities);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving amenities by type {AmenityType}", amenityType);
                return StatusCode(500, "An error occurred while retrieving amenities");
            }
        }

        /// <summary>
        /// Get amenity by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<AmenityDto>> GetAmenity(
            Int64 id,
            [FromQuery] bool includeOptions = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var amenity = includeOptions
                    ? await _amenityService.GetAmenityWithOptionsAsync(id, cancellationToken)
                    : await _amenityService.GetAmenityByIdAsync(id, cancellationToken);

                if (amenity == null)
                {
                    return NotFound($"Amenity with ID {id} not found");
                }

                var response = _mapper.Map<AmenityDto>(amenity);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving amenity with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the amenity");
            }
        }

        /// <summary>
        /// Create a new amenity
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<AmenityDto>> CreateAmenity(
            AmenityDto amenityDto,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var amenity = _mapper.Map<Amenity>(amenityDto);
                var createdAmenity = await _amenityService.CreateAmenityAsync(amenity, cancellationToken);
                var response = _mapper.Map<AmenityDto>(createdAmenity);

                return CreatedAtAction(nameof(GetAmenity), new { id = createdAmenity.Id }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating amenity");
                return StatusCode(500, "An error occurred while creating the amenity");
            }
        }

        /// <summary>
        /// Update an existing amenity
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<AmenityDto>> UpdateAmenity(
            Int64 id,
            AmenityDto amenityDto,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var amenity = _mapper.Map<Amenity>(amenityDto);
                var updatedAmenity = await _amenityService.UpdateAmenityAsync(id, amenity, cancellationToken);
                var response = _mapper.Map<AmenityDto>(updatedAmenity);

                return Ok(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Amenity with ID {Id} not found for update", id);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating amenity with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the amenity");
            }
        }

        /// <summary>
        /// Delete an amenity
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> DeleteAmenity(Int64 id, CancellationToken cancellationToken = default)
        {
            try
            {
                var exists = await _amenityService.AmenityExistsAsync(id, cancellationToken);
                if (!exists)
                {
                    return NotFound(false);
                }

                var result = await _amenityService.DeleteAmenityAsync(id, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting amenity with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the amenity");
            }
        }
    }
}
