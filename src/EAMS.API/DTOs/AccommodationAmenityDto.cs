using System.ComponentModel.DataAnnotations;

namespace EAMS.API.DTOs
{
    public class AccommodationAmenityDto
    {
        public Int64 Id { get; set; }

        [StringLength(500, ErrorMessage = "Note cannot exceed 500 characters")]
        public string? Note { get; set; }

        [Required(ErrorMessage = "Accommodation ID is required")]
        public Int64 AccommodationId { get; set; }

        [Required(ErrorMessage = "Amenity ID is required")]
        public Int64 AmenityId { get; set; }

        [Required(ErrorMessage = "Amenity Option ID is required")]
        public Int64 AmenityOptionId { get; set; }

        public AccommodationDto? Accommodation { get; set; }
        public AmenityDto? Amenity { get; set; }
        public AmenityOptionsDto? AmenityOption { get; set; }
    }
}
