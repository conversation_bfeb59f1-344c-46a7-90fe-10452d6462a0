using System.ComponentModel.DataAnnotations;

namespace EAMS.API.DTOs
{
    public class AmenityOptionsDto
    {
        public Int64 Id { get; set; }

        [Required(ErrorMessage = "Name is required")]
        [StringLength(200, ErrorMessage = "Name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Display text is required")]
        [StringLength(300, ErrorMessage = "Display text cannot exceed 300 characters")]
        public string DisplayText { get; set; } = string.Empty;

        [Required(ErrorMessage = "Icon is required")]
        [StringLength(100, ErrorMessage = "Icon cannot exceed 100 characters")]
        public string Icon { get; set; } = string.Empty;

        [Required(ErrorMessage = "Color is required")]
        [StringLength(50, ErrorMessage = "Color cannot exceed 50 characters")]
        public string Color { get; set; } = string.Empty;

        [Required(ErrorMessage = "Amenity ID is required")]
        public Int64 AmenityId { get; set; }

        public AmenityDto? Amenity { get; set; }
    }
}
