using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;

namespace EAMS.API.Mappings
{
    public class AccommodationProfile : Profile
    {
        public AccommodationProfile()
        {
            CreateMap<Accommodation, AccommodationDto>()
                .ForMember(dest => dest.Latitude, opt => opt.MapFrom(src => (double?)src.Location?.Latitude))
                .ForMember(dest => dest.Longitude, opt => opt.MapFrom(src => (double?)src.Location?.Longitude))
                .ReverseMap()
                .ForPath(src => src.Location,
                    opt => opt.MapFrom(dest =>
                        dest.Latitude.HasValue && dest.Longitude.HasValue
                            ? GeoPoint.Create(dest.Latitude.Value, dest.Longitude.Value)
                            : null));
        }
    }
}