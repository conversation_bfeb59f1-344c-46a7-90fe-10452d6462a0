@EAMS.API_HostAddress = http://localhost:5226

GET {{EAMS.API_HostAddress}}/weatherforecast/
Accept: application/json

###

### Test Accommodation CRUD Operations

### Get all accommodations
GET {{EAMS.API_HostAddress}}/api/accommodation
Accept: application/json

### Get accommodation by ID
GET {{EAMS.API_HostAddress}}/api/accommodation/1
Accept: application/json

### Get accommodation by ID with amenities
GET {{EAMS.API_HostAddress}}/api/accommodation/1?includeAmenities=true
Accept: application/json

### Create new accommodation
POST {{EAMS.API_HostAddress}}/api/accommodation
Content-Type: application/json

{
  "name": "Test Hotel",
  "street": "123 Test Street",
  "suburb": "Test Suburb",
  "postcode": "1234",
  "state": "NSW",
  "region": "Metropolitan",
  "phone": "0412345678",
  "email": "<EMAIL>",
  "website": "https://testhotel.com",
  "accommodationType": "Hotel",
  "density": "Medium",
  "duration": "ShortTerm",
  "inactive": false,
  "amenityIds": []
}

### Update accommodation
PUT {{EAMS.API_HostAddress}}/api/accommodation/1
Content-Type: application/json

{
  "name": "Updated Test Hotel",
  "street": "456 Updated Street",
  "suburb": "Updated Suburb",
  "postcode": "5678",
  "state": "VIC",
  "region": "Regional",
  "phone": "0487654321",
  "email": "<EMAIL>",
  "website": "https://updatedtesthotel.com",
  "accommodationType": "Motel",
  "density": "Low",
  "duration": "MediumTerm",
  "inactive": false,
  "amenityIds": []
}

### Delete accommodation
DELETE {{EAMS.API_HostAddress}}/api/accommodation/1

###

### Test Amenity CRUD Operations

### Get all amenities
GET {{EAMS.API_HostAddress}}/api/amenity
Accept: application/json

### Get all amenities with options
GET {{EAMS.API_HostAddress}}/api/amenity?includeOptions=true
Accept: application/json

### Get amenities by type
GET {{EAMS.API_HostAddress}}/api/amenity/by-type/Safety
Accept: application/json

### Get amenity by ID
GET {{EAMS.API_HostAddress}}/api/amenity/1
Accept: application/json

### Get amenity by ID with options
GET {{EAMS.API_HostAddress}}/api/amenity/1?includeOptions=true
Accept: application/json

### Create new amenity
POST {{EAMS.API_HostAddress}}/api/amenity
Content-Type: application/json

{
  "name": "Swimming Pool",
  "amenityType": "Amenity",
  "helpText": "On-site swimming pool facility"
}

### Update amenity
PUT {{EAMS.API_HostAddress}}/api/amenity/1
Content-Type: application/json

{
  "id": 1,
  "name": "Updated Swimming Pool",
  "amenityType": "Amenity",
  "helpText": "Updated on-site swimming pool facility with heating"
}

### Delete amenity
DELETE {{EAMS.API_HostAddress}}/api/amenity/1

###

### Test Amenity Options CRUD Operations

### Get all amenity options
GET {{EAMS.API_HostAddress}}/api/amenityoptions
Accept: application/json

### Get amenity options by amenity ID
GET {{EAMS.API_HostAddress}}/api/amenityoptions/by-amenity/1
Accept: application/json

### Get amenity option by ID
GET {{EAMS.API_HostAddress}}/api/amenityoptions/1
Accept: application/json

### Get amenity option by ID with amenity
GET {{EAMS.API_HostAddress}}/api/amenityoptions/1?includeAmenity=true
Accept: application/json

### Create new amenity option
POST {{EAMS.API_HostAddress}}/api/amenityoptions
Content-Type: application/json

{
  "name": "Indoor Pool",
  "displayText": "Indoor Swimming Pool",
  "icon": "pool-indoor",
  "color": "#0066CC",
  "amenityId": 1
}

### Update amenity option
PUT {{EAMS.API_HostAddress}}/api/amenityoptions/1
Content-Type: application/json

{
  "id": 1,
  "name": "Updated Indoor Pool",
  "displayText": "Updated Indoor Swimming Pool",
  "icon": "pool-indoor-updated",
  "color": "#0077DD",
  "amenityId": 1
}

### Delete amenity option
DELETE {{EAMS.API_HostAddress}}/api/amenityoptions/1

###

### Test Accommodation Amenity CRUD Operations

### Get all accommodation amenities
GET {{EAMS.API_HostAddress}}/api/accommodationamenity
Accept: application/json

### Get all accommodation amenities with details
GET {{EAMS.API_HostAddress}}/api/accommodationamenity?includeDetails=true
Accept: application/json

### Get accommodation amenities by accommodation ID
GET {{EAMS.API_HostAddress}}/api/accommodationamenity/by-accommodation/1
Accept: application/json

### Get accommodation amenities by amenity ID
GET {{EAMS.API_HostAddress}}/api/accommodationamenity/by-amenity/1
Accept: application/json

### Get accommodation amenities by amenity option ID
GET {{EAMS.API_HostAddress}}/api/accommodationamenity/by-amenity-option/1
Accept: application/json

### Get accommodation amenity by ID
GET {{EAMS.API_HostAddress}}/api/accommodationamenity/1
Accept: application/json

### Get accommodation amenity by ID with details
GET {{EAMS.API_HostAddress}}/api/accommodationamenity/1?includeDetails=true
Accept: application/json

### Check if combination exists
GET {{EAMS.API_HostAddress}}/api/accommodationamenity/exists?accommodationId=1&amenityId=1&amenityOptionId=1
Accept: application/json

### Create new accommodation amenity
POST {{EAMS.API_HostAddress}}/api/accommodationamenity
Content-Type: application/json

{
  "note": "Pool is available 24/7",
  "accommodationId": 1,
  "amenityId": 1,
  "amenityOptionId": 1
}

### Update accommodation amenity
PUT {{EAMS.API_HostAddress}}/api/accommodationamenity/1
Content-Type: application/json

{
  "id": 1,
  "note": "Updated: Pool is available 6AM-10PM",
  "accommodationId": 1,
  "amenityId": 1,
  "amenityOptionId": 1
}

### Delete accommodation amenity
DELETE {{EAMS.API_HostAddress}}/api/accommodationamenity/1
