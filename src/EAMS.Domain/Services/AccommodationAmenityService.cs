using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services
{
    public class AccommodationAmenityService : IAccommodationAmenityService
    {
        private readonly IAccommodationAmenityRepository _accommodationAmenityRepository;

        public AccommodationAmenityService(IAccommodationAmenityRepository accommodationAmenityRepository)
        {
            _accommodationAmenityRepository = accommodationAmenityRepository;
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetAllAccommodationAmenitiesAsync(CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetAllAsync(cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetAllWithDetailsAsync(CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetAllWithDetailsAsync(cancellationToken);
        }

        public async Task<AccommodationAmenity?> GetAccommodationAmenityByIdAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetByIdAsync(id, cancellationToken);
        }

        public async Task<AccommodationAmenity?> GetWithDetailsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetWithDetailsAsync(id, cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetByAccommodationIdAsync(Int64 accommodationId, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetByAccommodationIdAsync(accommodationId, cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetByAmenityIdAsync(amenityId, cancellationToken);
        }

        public async Task<IEnumerable<AccommodationAmenity>> GetByAmenityOptionIdAsync(Int64 amenityOptionId, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.GetByAmenityOptionIdAsync(amenityOptionId, cancellationToken);
        }

        public async Task<AccommodationAmenity> CreateAccommodationAmenityAsync(AccommodationAmenity accommodationAmenity, CancellationToken cancellationToken = default)
        {
            // Check if the combination already exists
            var exists = await _accommodationAmenityRepository.ExistsAsync(
                accommodationAmenity.AccommodationId, 
                accommodationAmenity.AmenityId, 
                accommodationAmenity.AmenityOptionId, 
                cancellationToken);

            if (exists)
            {
                throw new InvalidOperationException("This combination of Accommodation, Amenity, and AmenityOption already exists");
            }

            var createdAccommodationAmenity = await _accommodationAmenityRepository.AddAsync(accommodationAmenity, cancellationToken);
            await _accommodationAmenityRepository.SaveChangesAsync(cancellationToken);
            return createdAccommodationAmenity;
        }

        public async Task<AccommodationAmenity> UpdateAccommodationAmenityAsync(Int64 id, AccommodationAmenity accommodationAmenity, CancellationToken cancellationToken = default)
        {
            var existingAccommodationAmenity = await _accommodationAmenityRepository.GetByIdAsync(id, cancellationToken);
            if (existingAccommodationAmenity == null)
            {
                throw new InvalidOperationException($"AccommodationAmenity with ID {id} not found");
            }

            accommodationAmenity.Id = id; // Ensure the ID is preserved
            var result = await _accommodationAmenityRepository.UpdateAsync(accommodationAmenity, cancellationToken);
            await _accommodationAmenityRepository.SaveChangesAsync(cancellationToken);
            return result;
        }

        public async Task<bool> DeleteAccommodationAmenityAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            var result = await _accommodationAmenityRepository.DeleteAsync(id, cancellationToken);
            if (result)
            {
                await _accommodationAmenityRepository.SaveChangesAsync(cancellationToken);
            }
            return result;
        }

        public async Task<bool> AccommodationAmenityExistsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.ExistsAsync(id, cancellationToken);
        }

        public async Task<bool> CombinationExistsAsync(Int64 accommodationId, Int64 amenityId, Int64 amenityOptionId, CancellationToken cancellationToken = default)
        {
            return await _accommodationAmenityRepository.ExistsAsync(accommodationId, amenityId, amenityOptionId, cancellationToken);
        }
    }
}
