using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services
{
    public class AmenityOptionsService : IAmenityOptionsService
    {
        private readonly IAmenityOptionsRepository _amenityOptionsRepository;

        public AmenityOptionsService(IAmenityOptionsRepository amenityOptionsRepository)
        {
            _amenityOptionsRepository = amenityOptionsRepository;
        }

        public async Task<IEnumerable<AmenityOptions>> GetAllAmenityOptionsAsync(CancellationToken cancellationToken = default)
        {
            return await _amenityOptionsRepository.GetAllAsync(cancellationToken);
        }

        public async Task<AmenityOptions?> GetAmenityOptionByIdAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _amenityOptionsRepository.GetByIdAsync(id, cancellationToken);
        }

        public async Task<AmenityOptions?> GetAmenityOptionWithAmenityAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _amenityOptionsRepository.GetOptionWithAmenityAsync(id, cancellationToken);
        }

        public async Task<IEnumerable<AmenityOptions>> GetOptionsByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default)
        {
            return await _amenityOptionsRepository.GetOptionsByAmenityIdAsync(amenityId, cancellationToken);
        }

        public async Task<AmenityOptions> CreateAmenityOptionAsync(AmenityOptions amenityOption, CancellationToken cancellationToken = default)
        {
            var createdAmenityOption = await _amenityOptionsRepository.AddAsync(amenityOption, cancellationToken);
            await _amenityOptionsRepository.SaveChangesAsync(cancellationToken);
            return createdAmenityOption;
        }

        public async Task<AmenityOptions> UpdateAmenityOptionAsync(Int64 id, AmenityOptions amenityOption, CancellationToken cancellationToken = default)
        {
            var existingAmenityOption = await _amenityOptionsRepository.GetByIdAsync(id, cancellationToken);
            if (existingAmenityOption == null)
            {
                throw new InvalidOperationException($"AmenityOption with ID {id} not found");
            }

            amenityOption.Id = id; // Ensure the ID is preserved
            var result = await _amenityOptionsRepository.UpdateAsync(amenityOption, cancellationToken);
            await _amenityOptionsRepository.SaveChangesAsync(cancellationToken);
            return result;
        }

        public async Task<bool> DeleteAmenityOptionAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            var result = await _amenityOptionsRepository.DeleteAsync(id, cancellationToken);
            if (result)
            {
                await _amenityOptionsRepository.SaveChangesAsync(cancellationToken);
            }
            return result;
        }

        public async Task<bool> AmenityOptionExistsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _amenityOptionsRepository.ExistsAsync(id, cancellationToken);
        }
    }
}
