using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services
{
    public class AmenityService : IAmenityService
    {
        private readonly IAmenityRepository _amenityRepository;

        public AmenityService(IAmenityRepository amenityRepository)
        {
            _amenityRepository = amenityRepository;
        }

        public async Task<IEnumerable<Amenity>> GetAllAmenitiesAsync(CancellationToken cancellationToken = default)
        {
            return await _amenityRepository.GetAllAsync(cancellationToken);
        }

        public async Task<IEnumerable<Amenity>> GetAmenitiesWithOptionsAsync(CancellationToken cancellationToken = default)
        {
            return await _amenityRepository.GetAmenitiesWithOptionsAsync(cancellationToken);
        }

        public async Task<Amenity?> GetAmenityByIdAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _amenityRepository.GetByIdAsync(id, cancellationToken);
        }

        public async Task<Amenity?> GetAmenityWithOptionsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _amenityRepository.GetAmenityWithOptionsAsync(id, cancellationToken);
        }

        public async Task<IEnumerable<Amenity>> GetAmenitiesByTypeAsync(AmenityType amenityType, CancellationToken cancellationToken = default)
        {
            return await _amenityRepository.GetAmenitiesByTypeAsync(amenityType, cancellationToken);
        }

        public async Task<Amenity> CreateAmenityAsync(Amenity amenity, CancellationToken cancellationToken = default)
        {
            var createdAmenity = await _amenityRepository.AddAsync(amenity, cancellationToken);
            await _amenityRepository.SaveChangesAsync(cancellationToken);
            return createdAmenity;
        }

        public async Task<Amenity> UpdateAmenityAsync(Int64 id, Amenity amenity, CancellationToken cancellationToken = default)
        {
            var existingAmenity = await _amenityRepository.GetByIdAsync(id, cancellationToken);
            if (existingAmenity == null)
            {
                throw new InvalidOperationException($"Amenity with ID {id} not found");
            }

            amenity.Id = id; // Ensure the ID is preserved
            var result = await _amenityRepository.UpdateAsync(amenity, cancellationToken);
            await _amenityRepository.SaveChangesAsync(cancellationToken);
            return result;
        }

        public async Task<bool> DeleteAmenityAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            var result = await _amenityRepository.DeleteAsync(id, cancellationToken);
            if (result)
            {
                await _amenityRepository.SaveChangesAsync(cancellationToken);
            }
            return result;
        }

        public async Task<bool> AmenityExistsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _amenityRepository.ExistsAsync(id, cancellationToken);
        }
    }
}
