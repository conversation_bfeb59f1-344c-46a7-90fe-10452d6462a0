using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services
{
    public class AccommodationService : IAccommodationService
    {
        private readonly IAccommodationRepository _accommodationRepository;

        public AccommodationService(IAccommodationRepository accommodationRepository)
        {
            _accommodationRepository = accommodationRepository;
        }

        public async Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync()
        {
            return await _accommodationRepository.GetAllAsync();
        }

        public async Task<Accommodation?> GetAccommodationByIdAsync(Int64 id)
        {
            return await _accommodationRepository.GetByIdAsync(id);
        }

        public async Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation)
        {
            var createdAccommodation = await _accommodationRepository.AddAsync(accommodation);
            await _accommodationRepository.SaveChangesAsync();
            return createdAccommodation;
        }

        public async Task<Accommodation> UpdateAccommodationAsync(Accommodation accommodation)
        {
            var result = await _accommodationRepository.UpdateAsync(accommodation);
            await _accommodationRepository.SaveChangesAsync();
            return result;
        }

        public async Task<bool> DeleteAccommodationAsync(Int64 id)
        {
            var result = await _accommodationRepository.DeleteAsync(id);
            if (result)
            {
                await _accommodationRepository.SaveChangesAsync();
            }
            return result;
        }

        public async Task<bool> AccommodationExistsAsync(Int64 id)
        {
            return await _accommodationRepository.ExistsAsync(id);
        }
    }
}
