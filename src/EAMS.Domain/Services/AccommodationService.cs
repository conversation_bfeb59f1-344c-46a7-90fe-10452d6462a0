using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services
{
    public class AccommodationService : IAccommodationService
    {
        private readonly IAccommodationRepository _accommodationRepository;

        public AccommodationService(IAccommodationRepository accommodationRepository)
        {
            _accommodationRepository = accommodationRepository;
        }

        public async Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync()
        {
            return await _accommodationRepository.GetAllAsync();
        }

        public async Task<Accommodation?> GetAccommodationByIdAsync(Int64 id)
        {
            return await _accommodationRepository.GetByIdAsync(id);
        }

        public async Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation)
        {
            var createdAccommodation = await _accommodationRepository.AddAsync(accommodation);
            await _accommodationRepository.SaveChangesAsync();
            return createdAccommodation;
        }

        public async Task<Accommodation> UpdateAccommodationAsync(Accommodation accommodation)
        {
            var result = await _accommodationRepository.UpdateAsync(accommodation);
            await _accommodationRepository.SaveChangesAsync();
            return result;
        }

        public async Task<bool> DeleteAccommodationAsync(Int64 id)
        {
            var result = await _accommodationRepository.DeleteAsync(id);
            if (result)
            {
                await _accommodationRepository.SaveChangesAsync();
            }
            return result;
        }

        public async Task<bool> AccommodationExistsAsync(Int64 id)
        {
            return await _accommodationRepository.ExistsAsync(id);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsNearLocationAsync(
            double latitude, double longitude, double radiusKm)
        {
            return await _accommodationRepository.GetAccommodationsNearLocationAsync(latitude, longitude, radiusKm);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsByRegionAsync(Region region)
        {
            return await _accommodationRepository.GetAccommodationsByRegionAsync(region);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsByTypeAsync(AccommodationType accommodationType)
        {
            return await _accommodationRepository.GetAccommodationsByTypeAsync(accommodationType);
        }

        public async Task<IEnumerable<Accommodation>> GetAccommodationsWithLocationAsync()
        {
            return await _accommodationRepository.GetAccommodationsWithLocationAsync();
        }

        public async Task<Accommodation> SetAccommodationLocationAsync(Int64 id, double latitude, double longitude)
        {
            var accommodation = await _accommodationRepository.GetByIdAsync(id);
            if (accommodation == null)
            {
                throw new InvalidOperationException($"Accommodation with ID {id} not found");
            }

            var geoPoint = GeoPoint.Create(latitude, longitude);
            accommodation.SetLocation(geoPoint);

            var result = await _accommodationRepository.UpdateAsync(accommodation);
            await _accommodationRepository.SaveChangesAsync();
            return result;
        }

        public async Task<double?> CalculateDistanceBetweenAccommodationsAsync(Int64 accommodation1Id, Int64 accommodation2Id)
        {
            var accommodation1 = await _accommodationRepository.GetByIdAsync(accommodation1Id);
            var accommodation2 = await _accommodationRepository.GetByIdAsync(accommodation2Id);

            if (accommodation1?.Location == null || accommodation2?.Location == null)
            {
                return null;
            }

            return accommodation1.Location.DistanceTo(accommodation2.Location);
        }
    }
}
