using EAMS.Domain.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EAMS.Domain.Entities;

public class Amenity : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public AmenityType AmenityType { get; set; }
    public string HelpText { get; set; } = string.Empty;

    // Navigation properties
    public ICollection<AmenityOptions> AmenityOptions { get; set; } = new List<AmenityOptions>();
    public ICollection<AccommodationAmenity> AccommodationAmenities { get; set; } = new List<AccommodationAmenity>();
}

public class AmenityOptions : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string DisplayText { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;

    // Foreign key
    public Int64 AmenityId { get; set; }

    // Navigation properties
    public Amenity Amenity { get; set; } = null!;
    public ICollection<AccommodationAmenity> AccommodationAmenities { get; set; } = new List<AccommodationAmenity>();
}
