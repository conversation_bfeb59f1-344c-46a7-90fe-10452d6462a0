using System.ComponentModel.DataAnnotations;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Entities;

public class Accommodation : BaseEntity
{
    public string Name { get; set; } = string.Empty;

    public string StreetLine1 { get; set; } = string.Empty;

    public string? StreetLine2 { get; set; }

    public string Suburb { get; set; } = string.Empty;

    public string State { get; set; } = string.Empty;

    public string Postcode { get; set; } = string.Empty;

    public Region Region { get; set; }

    public string? Phone { get; set; }

    public string? Email { get; set; }

    public string? Website { get; set; }

    public AccommodationType AccommodationType { get; set; }

    public Density Density { get; set; }

    public List<Duration> Duration { get; set; } = new();

    public bool Inactive { get; set; } = false;

    public GeoPoint? Location { get; set; }

    public void SetLocation(GeoPoint? location) => Location = location;
}

public sealed record GeoPoint(double Latitude, double Longitude)
{
    public static GeoPoint Create(double latitude, double longitude)
    {
        ValidateCoordinates(latitude, longitude);
        return new GeoPoint(latitude, longitude);
    }

    private static void ValidateCoordinates(double latitude, double longitude)
    {
        if (latitude < -90 || latitude > 90)
            throw new ArgumentOutOfRangeException(nameof(latitude), "Latitude must be between -90 and 90 degrees");

        if (longitude < -180 || longitude > 180)
            throw new ArgumentOutOfRangeException(nameof(longitude), "Longitude must be between -180 and 180 degrees");
    }

    /// <summary>
    /// Calculate distance to another point using Haversine formula (in kilometers)
    /// </summary>
    public double DistanceTo(GeoPoint other)
    {
        const double earthRadiusKm = 6371.0;

        var lat1Rad = ToRadians(Latitude);
        var lat2Rad = ToRadians(other.Latitude);
        var deltaLatRad = ToRadians(other.Latitude - Latitude);
        var deltaLonRad = ToRadians(other.Longitude - Longitude);

        var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return earthRadiusKm * c;
    }

    private static double ToRadians(double degrees) => degrees * Math.PI / 180;

    /// <summary>
    /// Check if this point is within a certain radius of another point (in kilometers)
    /// </summary>
    public bool IsWithinRadius(GeoPoint center, double radiusKm)
    {
        return DistanceTo(center) <= radiusKm;
    }

    /// <summary>
    /// Get a formatted string representation of the coordinates
    /// </summary>
    public override string ToString() => $"{Latitude:F6}, {Longitude:F6}";
}