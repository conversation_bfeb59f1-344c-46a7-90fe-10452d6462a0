using System.ComponentModel.DataAnnotations;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Entities;


public class Accommodation : BaseEntity
{
    public string Name { get; set; } = string.Empty;

    public string Street { get; set; } = string.Empty;

    public string Suburb { get; set; } = string.Empty;

    public string Postcode { get; set; } = string.Empty;

    public string State { get; set; } = string.Empty;

    public Region Region { get; set; }

    public string? Phone { get; set; }

    public string? Email { get; set; }

    public string? Website { get; set; }

    public AccommodationType AccommodationType { get; set; }

    public Density Density { get; set; }

    public Duration Duration { get; set; }

    public bool Inactive { get; set; } = false;

    public ICollection<Amenity> Amenities { get; set; }
}

public class AccommodationAmenity : BaseEntity
{
    public string? note { get; set; }
    public Int64 AccommodationId { get; set; }
    public Int64 AmenityId { get; set; }
    public Int64 AmenityOptionId { get; set; }
}
