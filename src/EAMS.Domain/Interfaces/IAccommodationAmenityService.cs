using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces
{
    public interface IAccommodationAmenityService
    {
        Task<IEnumerable<AccommodationAmenity>> GetAllAccommodationAmenitiesAsync(CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetAllWithDetailsAsync(CancellationToken cancellationToken = default);
        Task<AccommodationAmenity?> GetAccommodationAmenityByIdAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<AccommodationAmenity?> GetWithDetailsAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetByAccommodationIdAsync(Int64 accommodationId, CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetByAmenityOptionIdAsync(Int64 amenityOptionId, CancellationToken cancellationToken = default);
        Task<AccommodationAmenity> CreateAccommodationAmenityAsync(AccommodationAmenity accommodationAmenity, CancellationToken cancellationToken = default);
        Task<AccommodationAmenity> UpdateAccommodationAmenityAsync(Int64 id, AccommodationAmenity accommodationAmenity, CancellationToken cancellationToken = default);
        Task<bool> DeleteAccommodationAmenityAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<bool> AccommodationAmenityExistsAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<bool> CombinationExistsAsync(Int64 accommodationId, Int64 amenityId, Int64 amenityOptionId, CancellationToken cancellationToken = default);
    }
}
