using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Interfaces
{
    public interface IAccommodationService
    {
        Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync();
        Task<Accommodation?> GetAccommodationByIdAsync(Int64 id);
        Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation);
        Task<Accommodation> UpdateAccommodationAsync(Accommodation accommodation);
        Task<bool> DeleteAccommodationAsync(Int64 id);
        Task<bool> AccommodationExistsAsync(Int64 id);

        // Location-based methods
        Task<IEnumerable<Accommodation>> GetAccommodationsNearLocationAsync(
            double latitude, double longitude, double radiusKm);
        Task<IEnumerable<Accommodation>> GetAccommodationsByRegionAsync(Region region);
        Task<IEnumerable<Accommodation>> GetAccommodationsByTypeAsync(AccommodationType accommodationType);
        Task<IEnumerable<Accommodation>> GetAccommodationsWithLocationAsync();

        // Utility methods
        Task<Accommodation> SetAccommodationLocationAsync(Int64 id, double latitude, double longitude);
        Task<double?> CalculateDistanceBetweenAccommodationsAsync(Int64 accommodation1Id, Int64 accommodation2Id);
    }
}
