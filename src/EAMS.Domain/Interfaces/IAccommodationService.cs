using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces
{
    public interface IAccommodationService
    {
        Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync();
        Task<Accommodation?> GetAccommodationByIdAsync(Int64 id);
        Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation);
        Task<Accommodation> UpdateAccommodationAsync(Accommodation accommodation);
        Task<bool> DeleteAccommodationAsync(Int64 id);
        Task<bool> AccommodationExistsAsync(Int64 id);
    }
}
