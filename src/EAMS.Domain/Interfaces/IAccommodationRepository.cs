using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Interfaces
{
    public interface IAccommodationRepository : IRepository<Accommodation>
    {
        Task<IEnumerable<Accommodation>> GetAccommodationsNearLocationAsync(
            double latitude, double longitude, double radiusKm, CancellationToken cancellationToken = default);

        Task<IEnumerable<Accommodation>> GetAccommodationsByRegionAsync(
            Region region, CancellationToken cancellationToken = default);

        Task<IEnumerable<Accommodation>> GetAccommodationsByTypeAsync(
            AccommodationType accommodationType, CancellationToken cancellationToken = default);

        Task<IEnumerable<Accommodation>> GetAccommodationsWithLocationAsync(
            CancellationToken cancellationToken = default);
    }
}
