using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Interfaces
{
    public interface IAmenityRepository : IRepository<Amenity>
    {
        Task<IEnumerable<Amenity>> GetAmenitiesWithOptionsAsync(CancellationToken cancellationToken = default);
        Task<Amenity?> GetAmenityWithOptionsAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<IEnumerable<Amenity>> GetAmenitiesByTypeAsync(AmenityType amenityType, CancellationToken cancellationToken = default);
    }
}
