using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces
{
    public interface IAmenityOptionsService
    {
        Task<IEnumerable<AmenityOptions>> GetAllAmenityOptionsAsync(CancellationToken cancellationToken = default);
        Task<AmenityOptions?> GetAmenityOptionByIdAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<AmenityOptions?> GetAmenityOptionWithAmenityAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<IEnumerable<AmenityOptions>> GetOptionsByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default);
        Task<AmenityOptions> CreateAmenityOptionAsync(AmenityOptions amenityOption, CancellationToken cancellationToken = default);
        Task<AmenityOptions> UpdateAmenityOptionAsync(Int64 id, AmenityOptions amenityOption, CancellationToken cancellationToken = default);
        Task<bool> DeleteAmenityOptionAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<bool> AmenityOptionExistsAsync(Int64 id, CancellationToken cancellationToken = default);
    }
}
