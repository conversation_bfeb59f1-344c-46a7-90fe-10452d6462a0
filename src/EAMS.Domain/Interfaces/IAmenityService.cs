using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Interfaces
{
    public interface IAmenityService
    {
        Task<IEnumerable<Amenity>> GetAllAmenitiesAsync(CancellationToken cancellationToken = default);
        Task<IEnumerable<Amenity>> GetAmenitiesWithOptionsAsync(CancellationToken cancellationToken = default);
        Task<Amenity?> GetAmenityByIdAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<Amenity?> GetAmenityWithOptionsAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<IEnumerable<Amenity>> GetAmenitiesByTypeAsync(AmenityType amenityType, CancellationToken cancellationToken = default);
        Task<Amenity> CreateAmenityAsync(Amenity amenity, CancellationToken cancellationToken = default);
        Task<Amenity> UpdateAmenityAsync(Int64 id, Amenity amenity, CancellationToken cancellationToken = default);
        Task<bool> DeleteAmenityAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<bool> AmenityExistsAsync(Int64 id, CancellationToken cancellationToken = default);
    }
}
