using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces
{
    public interface IAccommodationAmenityRepository : IRepository<AccommodationAmenity>
    {
        Task<IEnumerable<AccommodationAmenity>> GetByAccommodationIdAsync(Int64 accommodationId, CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetByAmenityIdAsync(Int64 amenityId, CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetByAmenityOptionIdAsync(Int64 amenityOptionId, CancellationToken cancellationToken = default);
        Task<AccommodationAmenity?> GetWithDetailsAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<IEnumerable<AccommodationAmenity>> GetAllWithDetailsAsync(CancellationToken cancellationToken = default);
        Task<bool> ExistsAsync(Int64 accommodationId, Int64 amenityId, Int64 amenityOptionId, CancellationToken cancellationToken = default);
    }
}
